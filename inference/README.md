# Inference Examples

Ready-to-use examples for exploring MiniCPM-o's powerful multimodal capabilities.

📖 [中文版本](./README_zh.md) | [View All Examples](../)

## Environment Setup

```bash
conda create -n minicpm python=3.10
conda activate minicpm
pip install -r requirements.txt 
```

## Available Examples

| Example | Description |
| ------- | ----------- |
| [Single Image](./minicpm-v4_5_single_image.md) | Image understanding with object detection and scene description |
| [Multi Images](./minicpm-v4_5_multi_images.md) | Compare and analyze multiple images for similarities and differences |
| [OCR](./minicpm-v4_5_ocr.md) | Optical character recognition with layout analysis |
| [Scene Text Recognition](./minicpm-v4_5_scene_text_recognize.md) | Text detection and recognition in real-world scenes |
| [PDF Parse](./minicpm-v4_5_pdf_parse.md) | Parse PDF documents with structured text extraction |
| [Video Understanding](./minicpm-v4_5_video_understanding.md) | Video content analysis and event extraction |
| [Speech-to-Text](./speech2text.md) | Speech recognition with multi-language support |
| [Text-to-Speech](./text2speech.md) | Speech synthesis with emotion control |
| [Voice Clone](./voice_clone.md) | Extract voice features for personalized speech synthesis |
| [RAG](./rag.md) | Retrieval-augmented generation with multimodal knowledge |
| [Agent](./agent.md) | AI agent system with tool-using capabilities |

## Quick Start

1. Choose an example notebook that interests you
2. Follow the setup instructions in each notebook
3. Run the cells step by step to see MiniCPM-o in action

For detailed usage instructions, please refer to the individual notebook files.