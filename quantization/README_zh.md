# 模型量化指南

通过模型量化降低存储和计算需求，使模型更适合在资源受限环境中部署。

📖 [English Version](./README.md) | [返回主页](../)

## 量化方法对比

| 方法 | 量化精度 | 压缩比 | 推理速度 | 内存占用 | 适用场景 |
|------|---------|--------|---------|---------|---------|
| **GGUF** | 4-bit/8-bit | 高 | 快 | 低 | CPU推理、边缘设备 |
| **BNB** | 4-bit/8-bit | 中 | 中 | 中 | GPU训练和推理 |
| **AWQ** | 4-bit | 高 | 快 | 低 | 高性能GPU推理 |

## 详细说明

### GGUF (GPT-Generated Unified Format)
- 专为CPU优化的量化格式
- 支持多种精度 (Q4_0, Q4_1, Q8_0等)
- 极低内存占用，适合移动设备和个人电脑
- 跨平台兼容性好

### BNB (Bits and Bytes) 
- 支持4-bit和8-bit量化
- 与transformers库无缝集成
- 支持训练时量化，适合微调和研究
- 精度损失最小

### AWQ (Activation-aware Weight Quantization)
- 激活感知的权重量化
- 4-bit量化精度，性能保持优秀
- 针对推理优化，适合生产环境
- 高吞吐量服务的理想选择

## 选择建议

- **移动/边缘设备**: GGUF - 专为CPU优化，内存占用最小
- **研究开发**: BNB - 易于集成，支持训练时量化  
- **生产环境**: AWQ - 推理性能优秀，适合大规模部署
- **个人使用**: GGUF - CPU友好，硬件要求低
