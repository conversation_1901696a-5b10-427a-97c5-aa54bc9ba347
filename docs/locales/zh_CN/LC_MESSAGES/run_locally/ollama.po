# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2025, OpenBMB
# This file is distributed under the same license as the MiniCPM-o Cookbook
# package.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: MiniCPM-o Cookbook \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-26 21:46+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: ../../source/run_locally/ollama.md:1 db53779158ba497ea12066d22e846a8c
msgid "Ollama"
msgstr "Ollama"

#: ../../source/run_locally/ollama.md:4 dd310ad927854857bbd6980f40f754be
msgid ""
"We've submitted a PR for MiniCPM-V 4.5 to the Ollama repo, and it's "
"currently under review for merging. In the meantime, you can use our code"
" via [this link](https://github.com/tc-mb/ollama/tree/Support-"
"MiniCPM-V-4.5).."
msgstr "我们已向 Ollama 仓库提交了 MiniCPM-V 4.5 的 PR，目前正在审核合并中。在此期间，您可以通过[此链接](https://github.com/tc-mb/ollama/tree/Support-MiniCPM-V-4.5)使用我们的代码。"

#: ../../source/run_locally/ollama.md:7 ec541de41ac24eb99bcc5bb3f795db39
msgid ""
"[Ollama](https://ollama.com/) helps you run LLMs locally with only a few "
"commands. It is available at macOS, Linux, and Windows. Now, MiniCPM-V "
"4.5 is officially on Ollama, and you can run it with one command:"
msgstr ""
"[Ollama](https://ollama.com/) 可以帮助你仅用几个命令在本地运行大语言模型。它支持 macOS、Linux 和 "
"Windows。现在，MiniCPM-V 4.5 已正式上线 Ollama，你只需一条命令即可运行："

#: ../../source/run_locally/ollama.md:13 1055f93917f14a37aa95f09a7b14d1eb
msgid ""
"Next, we introduce more detailed usages of Ollama for running MiniCPM-V "
"4.5."
msgstr "下面将介绍使用 Ollama 运行 MiniCPM-V 4.5 的更多详细用法。"

#: ../../source/run_locally/ollama.md:15 970f056d6f6f49ccbdaeaf4de2c4c6e0
msgid "Install Ollama"
msgstr "1. 安装 Ollama"

#: ../../source/run_locally/ollama.md:17 e402babc22e44af2861373ef600cb25c
msgid ""
"**macOS**: Download from "
"[https://ollama.com/download/Ollama.dmg](https://ollama.com/download/Ollama.dmg)."
msgstr "**macOS**: [下载](https://ollama.com/download/Ollama.dmg)"

#: ../../source/run_locally/ollama.md:19 56596f0b94674a59977d775f8ff5af4a
msgid ""
"**Windows**: Download from "
"[https://ollama.com/download/OllamaSetup.exe](https://ollama.com/download/OllamaSetup.exe)."
msgstr "**Windows**: [下载](https://ollama.com/download/OllamaSetup.exe)"

#: ../../source/run_locally/ollama.md:21 f7b7cc7ce2b944eebd36f85335d6074b
msgid ""
"**Linux**: `curl -fsSL https://ollama.com/install.sh | sh`, or refer to "
"the guide from "
"[ollama](https://github.com/ollama/ollama/blob/main/docs/linux.md)."
msgstr ""
"**Linux**：`curl -fsSL https://ollama.com/install.sh | sh`，或参考 [ollama "
"官方指南](https://github.com/ollama/ollama/blob/main/docs/linux.md)。"

#: ../../source/run_locally/ollama.md:23 3c1e7bd0b25745c7b0a5ed50ee8f0e34
msgid ""
"**Docker**: The official [Ollama Docker "
"image](https://hub.docker.com/r/ollama/ollama) `ollama/ollama` is "
"available on Docker Hub."
msgstr ""
"官方 [Ollama Docker 镜像](https://hub.docker.com/r/ollama/ollama) "
"`ollama/ollama` 可在 Docker Hub 获取。"

#: ../../source/run_locally/ollama.md:25 20bdaec38dce4da5822bb1d3da42ca6d
msgid "Build Ollama locally"
msgstr ""

#: ../../source/run_locally/ollama.md:27 fb59af55f2354330bd888c7341eadc15
msgid "Environment requirements:"
msgstr "环境要求"

#: ../../source/run_locally/ollama.md:29 cf897da870344044996f01dac68c0b94
msgid "[go](https://go.dev/doc/install) version 1.22 or above"
msgstr "go 版本 1.22 或更高"

#: ../../source/run_locally/ollama.md:30 90794f7aba694c6db754f180eac37601
msgid "cmake version 3.24 or above"
msgstr "cmake 版本 3.24 或更高"

#: ../../source/run_locally/ollama.md:31 185376a686a84022bfbf064980f4cdf0
msgid ""
"C/C++ Compiler e.g. Clang on macOS, [TDM-GCC](https://github.com/jmeubank"
"/tdm-gcc/releases) (Windows amd64) or [llvm-"
"mingw](https://github.com/mstorsjo/llvm-mingw) (Windows arm64), GCC/Clang"
" on Linux."
msgstr ""
"C/C++ 编译器，例如 macOS 上的 Clang，[TDM-GCC](https://github.com/jmeubank/tdm-"
"gcc/releases)（Windows amd64），[llvm-mingw](https://github.com/mstorsjo"
"/llvm-mingw)（Windows arm64），或 Linux 上的 GCC/Clang。"

#: ../../source/run_locally/ollama.md:33 bf0b02ed86c2470d8747ce6afd3e3303
msgid "Clone OpenBMB Ollama Fork:"
msgstr "克隆 OpenBMB 官方 Ollama 分支"

#: ../../source/run_locally/ollama.md:41 c81dfb7af14c46f29a7559b5b3c00da6
msgid "Then build and run Ollama from the root directory of the repository:"
msgstr "然后在仓库根目录下编译并运行 Ollama："

#: ../../source/run_locally/ollama.md:48 d548d1f1d86f452787efa2e03f7af487
msgid "Quickstart"
msgstr "2. 快速开始"

#: ../../source/run_locally/ollama.md:50 0c8ce633202a4fc3b15b5dccfc2c3e17
msgid ""
"Once the Ollama service has been built and launched, the MiniCPM-V/o "
"series models can be run using the following commands:"
msgstr "在构建并启动 Ollama 服务后，可通过以下命令运行 MiniCPM-V/o 系列模型:"

#: ../../source/run_locally/ollama.md:52 f7226e420aee40ea8821686f06c9a506
msgid "`./ollama run openbmb/minicpm-v4.5`"
msgstr "`./ollama run openbmb/minicpm-v4.5`"

#: ../../source/run_locally/ollama.md:53 c035dc9fd9c54615a793e8b763b257a0
msgid "`./ollama run openbmb/minicpm-o2.6`"
msgstr "`./ollama run openbmb/minicpm-o2.6`"

#: ../../source/run_locally/ollama.md:54 d70217bc0e244bf2ba6620c6244f4428
msgid "`./ollama run openbmb/minicpm-v2.6`"
msgstr "`./ollama run openbmb/minicpm-v2.6`"

#: ../../source/run_locally/ollama.md:55 f7226e420aee40ea8821686f06c9a506
msgid "`./ollama run openbmb/minicpm-v2.5`"
msgstr "`./ollama run openbmb/minicpm-v2.5`"

#: ../../source/run_locally/ollama.md:57 df7b1b25726c4f7c90e1669b5e9ce2e9
msgid "Command Line"
msgstr "命令行"

#: ../../source/run_locally/ollama.md:58 70349edbba244f62945298021c3a4025
msgid "Separate the input prompt and the image path with space."
msgstr "输入提示和图片路径用空格分隔。"

#: ../../source/run_locally/ollama.md:63 5e7dd66b50c24cc78dbcd59cba5bc725
msgid "API"
msgstr "API"

#: ../../source/run_locally/ollama.md:82 cd20dd90eeec448e894fce56c104b85d
msgid "Run Ollama with Your GGUF Files"
msgstr "使用你自己的 GGUF 文件运行 Ollama"

#: ../../source/run_locally/ollama.md:84 7907b5c48e104eb99f82bfa032c11143
msgid ""
"You can alse use Ollama with your own GGUF files of MiniCPM-V 4.5. For "
"the first step, you need to create a file called `Modelfile`. The content"
" of the file is shown below:"
msgstr ""
"你也可以使用自己 MiniCPM-V 4.5 的 GGUF 文件与 Ollama 配合使用。第一步，你需要创建一个名为 `Modelfile` "
"的文件，其内容如下所示："

#: ../../source/run_locally/ollama.md:100 86fb0587132840659cd3ca54a549875e
msgid "Parameter Descriptions:"
msgstr "参数说明："

#: ../../source/run_locally/ollama.md:3 524600cf2108463c93b276edf797e2f7
msgid "first from"
msgstr "第一个 from"

#: ../../source/run_locally/ollama.md:3 a007ff73870048bdb42a7904c1dfa5ea
msgid "second from"
msgstr "第二个 from"

#: ../../source/run_locally/ollama.md:3 b416b58752ac4831a3f58e6421b2b5d5
msgid "num_ctx"
msgstr "num_ctx"

#: ../../source/run_locally/ollama.md:3 4c4ff563c8ac4241935e694707fa5260
msgid "Your language GGUF model path"
msgstr "你的语言 GGUF 模型路径"

#: ../../source/run_locally/ollama.md:3 d7daab59c3f24a538d057c723f3fee54
msgid "Your vision GGUF model path"
msgstr "你的视觉 GGUF 模型路径"

#: ../../source/run_locally/ollama.md:3 6700ad82db7b40fd94ff56136e66a4a9
msgid "Max Model length"
msgstr "最大模型长度"

#: ../../source/run_locally/ollama.md:106 20bdaec38dce4da5822bb1d3da42ca6d
msgid "Create Ollama Model:"
msgstr "创建 Ollama 模型"

#: ../../source/run_locally/ollama.md:111 c30c4fe220924c24a2db2f1533e2e9f5
msgid "Run your Ollama model: In a new terminal window, run the model instance:"
msgstr "在新的终端窗口中运行模型实例："

#: ../../source/run_locally/ollama.md:117 ded46333ee33413c904bde569f188020
msgid "Enter the prompt and the image path, separated by a space."
msgstr "输入提示和图片路径，用空格分隔。"

#~ msgid "Requirements"
#~ msgstr "环境要求"

#~ msgid "**Non-quantized version:** Requires over 9GB of RAM."
#~ msgstr "**非量化版本：** 需要至少 9GB 内存。"

#~ msgid "**Quantized version:** Requires over 3GB of RAM."
#~ msgstr "**量化版本：** 需要至少 3GB 内存。"

#~ msgid "macOS"
#~ msgstr "macOS"

#~ msgid "Windows"
#~ msgstr "Windows"

#~ msgid "Linux"
#~ msgstr "Linux"

#~ msgid ""
#~ "[Manual install "
#~ "instructions](https://github.com/ollama/ollama/blob/main/docs/linux.md)"
#~ msgstr "[手动安装指南](https://github.com/ollama/ollama/blob/main/docs/linux.md)"

#~ msgid "Docker"
#~ msgstr "Docker"

#~ msgid "The MiniCPM-V 4 model can be used directly:"
#~ msgstr "MiniCPM-V 4 模型可直接使用："

#~ msgid "3. Customize model"
#~ msgstr "3. 自定义模型"

#~ msgid "**If the method above fails, please refer to the following guide.**"
#~ msgstr "**如果上述方法失败，请参考以下指南。**"

#~ msgid "gcc version 11.4.0 or above"
#~ msgstr "gcc 版本 11.4.0 或更高"

#~ msgid ""
#~ "[HuggingFace](https://huggingface.co/openbmb/openbmb/MiniCPM-V-4-gguf)"
#~ "   "
#~ "[ModelScope](https://modelscope.cn/models/OpenBMB/OpenBMB/MiniCPM-V-4-gguf)"
#~ msgstr ""
#~ "[HuggingFace](https://huggingface.co/openbmb/openbmb/MiniCPM-V-4-gguf)"
#~ "   "
#~ "[魔搭社区](https://modelscope.cn/models/OpenBMB/OpenBMB/MiniCPM-V-4-gguf)"

#~ msgid "Install Dependencies"
#~ msgstr "安装依赖"

#~ msgid "Build Ollama"
#~ msgstr "编译 Ollama"

#~ msgid "Start Ollama Service"
#~ msgstr "启动 Ollama 服务"

#~ msgid ""
#~ "Once the build is successful, start "
#~ "the Ollama service from its root "
#~ "directory:"
#~ msgstr "编译成功后，在 Ollama 根目录下启动服务："

#~ msgid "Create a ModelFile"
#~ msgstr "创建 ModelFile"

#~ msgid "Create and edit a ModelFile:"
#~ msgstr "创建并编辑 ModelFile："

#~ msgid "The content of the Modelfile should be as follows:"
#~ msgstr "Modelfile 内容如下："

#~ msgid "Run"
#~ msgstr "运行"

#~ msgid "Input Prompt"
#~ msgstr "输入提示"

#~ msgid "Deployment"
#~ msgstr "部署"

#~ msgid ""
#~ "If the method above fails, please "
#~ "refer to the following guide, or "
#~ "refer to the guide from "
#~ "[ollama](https://github.com/ollama/ollama/blob/main/docs/development.md)."
#~ msgstr ""
#~ "如果上述方法失败，请参考以下指南，或参考 [ollama "
#~ "官方开发文档](https://github.com/ollama/ollama/blob/main/docs/development.md)。"

#~ msgid "Download GGUF Model:"
#~ msgstr "下载 GGUF 模型"

#~ msgid "HuggingFace: https://huggingface.co/openbmb/MiniCPM-V-4-gguf"
#~ msgstr "HuggingFace: https://huggingface.co/openbmb/MiniCPM-V-4-gguf"

#~ msgid "ModelScope: https://modelscope.cn/models/OpenBMB/MiniCPM-V-4-gguf"
#~ msgstr "ModelScope: https://modelscope.cn/models/OpenBMB/MiniCPM-V-4-gguf"

#~ msgid ""
#~ "Visit the official website "
#~ "[Ollama](https://ollama.com/) and click download "
#~ "to install Ollama on your device. "
#~ "You can also search models on the"
#~ " website, where you can find the "
#~ "MiniCPM-V/o series models. Except for "
#~ "the default one, you can choose to"
#~ " run MiniCPM-V/o series models by:"
#~ msgstr ""
#~ "访问 [Ollama 官网](https://ollama.com/) 并点击下载，在你的设备上安装"
#~ " Ollama。你可以通过以下命令运行 MiniCPM-V/o 系列模型："

#~ msgid "Configure and build the project:"
#~ msgstr "配置并编译项目："

#~ msgid ""
#~ "As updates to the official Ollama "
#~ "version may occasionally introduce minor "
#~ "uncertainties, we provide an additional "
#~ "maintained [branch](https://github.com/tc-"
#~ "mb/ollama/tree/MIniCPM-V) to support stable "
#~ "execution of the MiniCPM-V series "
#~ "models. We sincerely appreciate the "
#~ "ongoing updates and support from the "
#~ "Ollama team."
#~ msgstr ""
#~ "由于 Ollama "
#~ "官方版本在更新过程中可能会出现轻微的不确定性，我们提供了一个额外维护的[分支](https://github.com/tc-"
#~ "mb/ollama/tree/MIniCPM-V)，以支持 MiniCPM-V 系列模型的稳定运行。我们衷心感谢"
#~ " Ollama 官方团队的持续更新与支持。"

#~ msgid "`./ollama run openbmb/minicpm-v4`"
#~ msgstr "`ollama run openbmb/minicpm-v4`"

