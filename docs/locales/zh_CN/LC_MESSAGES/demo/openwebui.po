# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2025, OpenBMB
# This file is distributed under the same license as the MiniCPM-o Cookbook
# package.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: MiniCPM-o Cookbook \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-26 21:46+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: ../../source/demo/openwebui.md:1 0c57a6737e7147b0818724f5ef27e402
msgid "OpenWebUI"
msgstr ""

#: ../../source/demo/openwebui.md:3 843276e3df1b49f7b03277e0e47f46ac
msgid "Overview"
msgstr "概述"

#: ../../source/demo/openwebui.md:5 15f4a527ab7e4cfda928e84961918017
msgid ""
"Open WebUI is an extensible web interface designed for Ollama with full "
"OpenAI API compatibility. It provides seamless interaction with MiniCPM-V"
" multimodal models through an intuitive chat interface."
msgstr ""
"Open WebUI 是一个为 Ollama 设计的可扩展 Web 界面，完全兼容 OpenAI API。它通过直观的聊天界面，提供了与 "
"MiniCPM-V 多模态模型的无缝交互。"

#: ../../source/demo/openwebui.md:7 5962f93b7cac455f83ab8e36df83791d
msgid ""
"MiniCPM-V is a series of efficient multimodal models with strong OCR "
"capabilities, supporting high-resolution images, multi-image reasoning, "
"and video understanding."
msgstr "MiniCPM-V 是一系列高效的多模态模型，具有强大的 OCR 能力，支持高分辨率图像、多图像推理和视频理解。"

#: ../../source/demo/openwebui.md:9 e43396e02a1c474c9a6c92cfafea403f
msgid "Requirements"
msgstr "环境要求"

#: ../../source/demo/openwebui.md:10 3c5a932919f24c44a1caa63aeaed1d3b
msgid "Python 3.11+"
msgstr "Python 3.11+"

#: ../../source/demo/openwebui.md:11 683ac1f1831c411b9c1259fffaa2ed20
msgid "Docker (recommended) or local Python environment"
msgstr "Docker（推荐）或本地 Python 环境"

#: ../../source/demo/openwebui.md:12 fd1b149beb524fb199a7a40dc7463093
msgid "18GB+ RAM (24GB+ recommended)"
msgstr "18GB+ 内存（推荐 24GB+）"

#: ../../source/demo/openwebui.md:13 76d993788f4d4292b9a9075c1545fe6d
msgid "CUDA-compatible GPU (for local inference)"
msgstr "兼容 CUDA 的 GPU（用于本地推理）"

#: ../../source/demo/openwebui.md:15 1575fcb85a2c4bf5ba350e1f53a8dad0
msgid "Quick Setup"
msgstr "快速设置"

#: ../../source/demo/openwebui.md:17 c2b8a69007f64fd59cfdffc20904b88d
msgid "Option 1: Docker (Recommended)"
msgstr "方案一：Docker（推荐）"

#: ../../source/demo/openwebui.md:28 509dc16c5f2c48bf8cb131d8025ef480
msgid "Option 2: pip Installation"
msgstr "方案二：pip 安装"

#: ../../source/demo/openwebui.md:35 bb9721d3846645808ab29a40e5a608d1
msgid "Access at: http://localhost:8080"
msgstr "访问地址：http://localhost:8080"

#: ../../source/demo/openwebui.md:37 d018cf48912243d5a68e3c328da2e3b8
msgid "Model Deployment"
msgstr "模型部署"

#: ../../source/demo/openwebui.md:39 6b83d043b3f24b228f07f93d78fe68e9
msgid "Using Ollama (Easiest)"
msgstr "使用 Ollama（最简单）"

#: ../../source/demo/openwebui.md:49 992d88c4934d4cdca1f949716883ca28
msgid ""
"Configure in Open WebUI: Settings → Connections → Ollama API → "
"`http://localhost:11434`"
msgstr "在 Open WebUI 中配置：设置 → 连接 → Ollama API → `http://localhost:11434`"

#: ../../source/demo/openwebui.md:51 f8b7583c4bc944bbb0a53385958a6312
msgid "Using vLLM (High Performance)"
msgstr "使用 vLLM（高性能）"

#: ../../source/demo/openwebui.md:62 d8e8c1d4cf0646e6a8bab9360c12b5e6
msgid "Configure in Open WebUI: Settings → Connections → OpenAI API"
msgstr "在 Open WebUI 中配置：设置 → 连接 → OpenAI API"

#: ../../source/demo/openwebui.md:63 33accda4916249ad98ef3a9a18b32e83
msgid "API Base URL: `http://localhost:8000/v1`"
msgstr "API 基础 URL：`http://localhost:8000/v1`"

#: ../../source/demo/openwebui.md:64 0bd577205221404f9edc7d6f7b857767
msgid "API Key: `token-abc123`"
msgstr "API 密钥：`token-abc123`"

#: ../../source/demo/openwebui.md:66 910a16c2b44e4c50b6ef4d42f4b24b33
msgid "Using SGLang (Structured Generation)"
msgstr "使用 SGLang（结构化生成）"

#: ../../source/demo/openwebui.md:80 ddb93794493d405aae53c9ff1fad79c6
msgid "Configure API Base URL: `http://localhost:30000/v1`"
msgstr "配置 API 基础 URL：`http://localhost:30000/v1`"

#: ../../source/demo/openwebui.md:82 fce5af9b0e3046d49930dfc8197cec96
msgid "Usage Examples"
msgstr "使用示例"

#: ../../source/demo/openwebui.md:84 3d0521586af04901a727f66e50f7f040
msgid "Image Understanding"
msgstr "图像理解"

#: ../../source/demo/openwebui.md:85 891403d580084650b9f5ed3dfda9f4ef
msgid "Upload an image and ask:"
msgstr "上传一张图片并提问："

#: ../../source/demo/openwebui.md:90 9d49df6a44d3447c8e8d6871161ebd3b
msgid "OCR Text Extraction"
msgstr "OCR 文本提取"

#: ../../source/demo/openwebui.md:95 e4a1b9df9abc4c8eab3aa4eec819eee3
msgid "Multi-image Comparison"
msgstr "多图对比"

#: ../../source/demo/openwebui.md:100 01aef1cea7664046adbba0c5d73f8cce
msgid "Document Analysis"
msgstr "文档分析"

#: ../../source/demo/openwebui.md:105 dfab1e9488254f3692fb4258616ef21b
msgid "API Integration"
msgstr "API 集成"

#: ../../source/demo/openwebui.md:107 9fc336eda35946acbbe587c4fbde36a2
msgid "Python Example"
msgstr "Python 示例"

#: ../../source/demo/openwebui.md:132 a5c3e3d505cb4e03a86c4ff0925907e5
msgid "Resources"
msgstr "资源"

#: ../../source/demo/openwebui.md:134 02f6a1da4e294338ba8de6659e20af65
msgid "[Open WebUI Documentation](https://docs.openwebui.com/)"
msgstr "[Open WebUI 文档](https://docs.openwebui.com/)"

#: ../../source/demo/openwebui.md:135 21697f0380f74f8c8ed07375217f49d9
msgid "[MiniCPM-V Models](https://huggingface.co/openbmb)"
msgstr "[MiniCPM-V 模型](https://huggingface.co/openbmb)"

#: ../../source/demo/openwebui.md:136 2561dd65817f4174ac2311e68b03f1eb
msgid "[Ollama Official Site](https://ollama.ai/)"
msgstr "[Ollama 官网](https://ollama.ai/)"

#: ../../source/demo/openwebui.md:137 993da9049aff4a37a850d64fcee7304a
msgid "[vLLM Documentation](https://docs.vllm.ai/)"
msgstr "[vLLM 文档](https://docs.vllm.ai/)"

#~ msgid "Open WebUI"
#~ msgstr "Open WebUI"

#~ msgid "OpenWebUI with MiniCPM-V Integration Guide"
#~ msgstr "OpenWebUI"

