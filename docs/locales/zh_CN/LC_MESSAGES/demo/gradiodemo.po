# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2025, OpenBMB
# This file is distributed under the same license as the MiniCPM-V & o
# Cookbook package.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: MiniCPM-V & o Cookbook \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-09-01 12:36+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: ../../source/demo/gradiodemo.md:1 46c7578a86ae4c368a4af3873fcf9b41
msgid "Gradio Web Demo"
msgstr "Gradio Web Demo"

#: ../../source/demo/gradiodemo.md:4 af925e780118458aafc5c138b753c41d
msgid "**Support:** MiniCPM-V 4.5, MiniCPM-V 4.0"
msgstr "**支持：** MiniCPM-V 4.5, MiniCPM-V 4.0"

#: ../../source/demo/gradiodemo.md:7 6c26e946a8a54b02ad9a40bf7118c00f
msgid ""
"Gradio Web Demo is a web interface demonstration service for MiniCPM-V, "
"supporting multimodal conversations with images and videos. The demo "
"consists of two parts: **server** and **client**"
msgstr ""
"Gradio Web 演示是 MiniCPM-V 的网页界面演示服务，支持图像和视频的多模态对话。该演示由两部分组成：**服务端** 和 "
"**客户端**"

#: ../../source/demo/gradiodemo.md:9 21c2353c974346a4b0fe8de976451519
msgid ""
"Before getting started, please obtain the relevant code via [this "
"link](https://github.com/OpenSQZ/MiniCPM-V-CookBook/tree/main/demo/web_demo/gradio)."
msgstr "在开始之前，请通过[此链接](https://github.com/OpenSQZ/MiniCPM-V-CookBook/tree/main/demo/web_demo/gradio)获取相关代码。"

#: ../../source/demo/gradiodemo.md:11 8228c45f1b7049c7ad2ed1683032cb43
msgid "Deployment Steps"
msgstr "部署步骤"

#: ../../source/demo/gradiodemo.md:13 d20781c34b9e4656b81e5e34402184d9
msgid "Server"
msgstr "服务端"

#: ../../source/demo/gradiodemo.md:24 ../../source/demo/gradiodemo.md:46
#: 22174f3121b640dfbf77aef5f6d50c37 417a174aa99846959d6981c1c6250f86
msgid "**Custom Parameters:**"
msgstr "**自定义参数：**"

#: ../../source/demo/gradiodemo.md:31 e0cf3f597f864e689f8ff45fc820a415
msgid "Client"
msgstr "客户端"

#: ../../source/demo/gradiodemo.md:53 6b4686b9fc7b4ef4933a44b903144edb
msgid "Access"
msgstr "访问"

#: ../../source/demo/gradiodemo.md:55 f053139e1e3e41f9b8de0b4bd0706a49
msgid ""
"By default, after the services are started, you can access the web demo "
"by visiting http://localhost:8889 in your browser."
msgstr "默认情况下，服务启动后，您可以在浏览器中访问 http://localhost:8889 来使用网页演示。"

#: ../../source/demo/gradiodemo.md:57 995ee33e06db4f308d640813af1daf65
msgid "![demo](./assets/demo.png)"
msgstr "![演示](./assets/demo.png)"

#: ../../source/demo/gradiodemo.md:57 18a69391393a491a87719e3a0e49089f
msgid "demo"
msgstr "演示"

