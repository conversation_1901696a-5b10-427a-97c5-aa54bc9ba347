# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2025, OpenBMB
# This file is distributed under the same license as the MiniCPM-o Cookbook
# package.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: MiniCPM-o Cookbook \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-25 21:48+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: ../../source/finetune/swift.md:1 72c63b63047f49eea73621e3155974c6
msgid "SWIFT"
msgstr "SWIFT"

#: ../../source/finetune/swift.md:4 7be0798e8c7a436ab2b61511affecd6b
msgid "**Support:** MiniCPM-V 4.0 / MiniCPM-V 2.6"
msgstr "**支持:** MiniCPM-V 4.0 / MiniCPM-V 2.6"

#: ../../source/finetune/swift.md:7 724c8c7d4b4d481bbf0e45ed4dac001d
msgid ""
"SWIFT is an efficient and scalable framework for fine-tuning large pre-"
"trained models with support for various parameter-efficient methods like "
"LoRA, Adapter, and Prompt Tuning."
msgstr "SWIFT 是一个高效且可扩展的框架，用于微调大型预训练模型，支持 LoRA、Adapter 和 Prompt Tuning 等多种参数高效方法。"

#: ../../source/finetune/swift.md:9 003a4bbd09aa48a698aae6d3422eb245
msgid "SWIFT Install"
msgstr "安装SWIFT"

#: ../../source/finetune/swift.md:11 d53f5c5141c947da8528c05e23f682e8
msgid "You can quickly install SWIFT using bash commands."
msgstr "您可以使用 bash 命令快速安装 SWIFT。"

#: ../../source/finetune/swift.md:20 ad73df6bdf154483ab680f204bd5a5c0
msgid "Train"
msgstr "训练"

#: ../../source/finetune/swift.md:22 2a67180baad64d089badd5912413aac5
msgid "Prepare the data"
msgstr "数据准备"

#: ../../source/finetune/swift.md:24 fe2d95acf0d84f67a1d3eec68f8a4eb7
msgid ""
"You can refer to the format below to construct your dataset. Custom "
"datasets support JSON and JSONL formats."
msgstr "您可以参考以下格式构建您的数据集。自定义数据集支持 JSON 和 JSONL 格式。"

#: ../../source/finetune/swift.md:32 17f53e6016024a8c88ed0c42e60af6c2
msgid ""
"Alternatively, you can also use datasets from ModelScope, such as the "
"image dataset [coco-en-"
"mini](https://modelscope.cn/datasets/modelscope/coco_2014_caption/summary)"
" or the video dataset [video-"
"chatgpt](https://modelscope.cn/datasets/swift/VideoChatGPT)."
msgstr ""
"或者，您也可以使用 ModelScope 的数据集，例如图像数据集 [coco-en-"
"mini](https://modelscope.cn/datasets/modelscope/coco_2014_caption/summary)"
" 或视频数据集 [video-"
"chatgpt](https://modelscope.cn/datasets/swift/VideoChatGPT)。"

#: ../../source/finetune/swift.md:34 3526f4b3759a4da4937127e2fabc5cb7
msgid "Image Fine-tuning"
msgstr "图像微调"

#: ../../source/finetune/swift.md:36 cddc812fc2e147d3a8fc497930965489
msgid ""
"We use the `coco-en-mini` dataset for fine-tuning, which involves "
"describing the content of images."
msgstr "我们使用 `coco-en-mini` 数据集进行微调，该数据集涉及描述图像内容。"

#: ../../source/finetune/swift.md:38 ../../source/finetune/swift.md:69
#: 63c8ffd5f86e4a9cab79ed351e3b40aa 65fbe904333645789348d5783e32b235
msgid "The following is the code configuration:"
msgstr "以下是代码配置："

#: ../../source/finetune/swift.md:49 ../../source/finetune/swift.md:79
#: 74fa6a8af0c24545aedb36764ba991f1 d73c5f41af3a49a88bd8dc862f5fc4b5
msgid "If you want to use a custom dataset, simply specify it as follows:"
msgstr "如果您想使用自定义数据集，只需按如下方式指定即可："

#: ../../source/finetune/swift.md:56 ../../source/finetune/swift.md:94
#: 168b9f77c2724ef4b1427a3a264315b9 8a26651851cd444493439d98380b2822
msgid "The inference script after fine-tuning is as follows:"
msgstr "微调后的推理脚本如下："

#: ../../source/finetune/swift.md:65 93356844665b40f69e67ae483f2517c8
msgid "Video Fine-tuning"
msgstr "视频微调"

#: ../../source/finetune/swift.md:67 38892d1eda8b4331a7b5f9079f8fa4e9
msgid ""
"We use the `video-chatgpt` dataset for fine-tuning, which involves "
"describing the content of images."
msgstr "我们使用 `video-chatgpt` 数据集进行微调，该数据集涉及描述视频内容。"

#: ../../source/finetune/swift.md:86 d8bd304b5c0645d792f96c448287b13d
msgid ""
"Custom datasets support JSON and JSONL formats. Below is an example of "
"the video dataset:"
msgstr "自定义数据集支持 JSON 和 JSONL 格式。以下是视频数据集的示例："

#: ../../source/finetune/swift.md:102 ea4ee95c8e00440c9b277aec049f3b16
msgid "Infer"
msgstr "推理"

#: ../../source/finetune/swift.md:104 194c1e9a13784de68176459c826efe4f
msgid ""
"Run the bash code will download the model of MiniCPM-V 2.6 and run the "
"inference"
msgstr "运行 bash 代码将下载 MiniCPM-V 2.6 模型并执行推理。"
