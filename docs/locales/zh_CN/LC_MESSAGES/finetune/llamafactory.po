# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2025, OpenBMB
# This file is distributed under the same license as the MiniCPM-o Cookbook
# package.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: MiniCPM-o Cookbook \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-09-03 12:36+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: ../../source/finetune/llamafactory.md:1 27c99f73dd434c699df9cc8d098bdafc
msgid "Llama Factory"
msgstr "Llama Factory"

#: ../../source/finetune/llamafactory.md:4 608cb06b111948418271df9a45d8d498
msgid "**Support:** MiniCPM-V 4.5 / MiniCPM-V 4.0 / MiniCPM-V 2.6 / MiniCPM-V 2.5"
msgstr "**支持:** MiniCPM-V 4.5 / MiniCPM-V 4.0 / MiniCPM-V 2.6 / MiniCPM-V 2.5"

#: ../../source/finetune/llamafactory.md:7 ee39d1a393144b638f126a135a96e648
msgid "Install LlamaFactory"
msgstr "安装 Llama Factory"

#: ../../source/finetune/llamafactory.md:9 65ac8228b23e4d4196ca2bbe779a6b52
msgid "Clone the LlamaFactory GitHub repository:"
msgstr "从克隆Llama Factory Github仓库代码"

#: ../../source/finetune/llamafactory.md:15 4a61ad0f52b74888866b356a53ff075b
msgid "Install LlamaFactory dependencies:"
msgstr "安装依赖"

#: ../../source/finetune/llamafactory.md:22 61f2b6ffc53a4faa99c7424d56eebac1
msgid "Prepare the Dataset"
msgstr "准备数据集"

#: ../../source/finetune/llamafactory.md:24 19580348a52c47a89949dbebc4e99dd5
msgid "Building Image Dataset"
msgstr "构建图片数据集"

#: ../../source/finetune/llamafactory.md:26 8645ead0e65e4f7eb3ca408fd5613c40
msgid ""
"Refer to the **mllm_demo.json** dataset under [LLaMA-"
"Factory/data](https://github.com/hiyouga/LLaMA-"
"Factory/blob/main/data/dataset_info.json) and construct your data in the "
"same format. The structure is as follows:"
msgstr ""
"参考 [LLaMA-Factory/data](https://github.com/hiyouga/LLaMA-"
"Factory/blob/main/data/dataset_info.json) 下的 **mllm_demo.json** "
"数据集，按照相同格式构建你的数据。结构如下："

#: ../../source/finetune/llamafactory.md:28 db2a76230fd84f3aab964f7a576e5447
msgid ""
"To use images in multi-turn conversations, add the `<image>` tag in the "
"user's content for each turn, and add the corresponding image paths in "
"the `images` field. The number of `<image>` tags should match the number "
"of values in `images`."
msgstr ""
"如需在多轮对话中使用图片，请在每轮用户内容中添加 `<image>` 标签，并在 `images` 字段中添加对应的图片路径。`<image>` "
"标签的数量应与 `images` 字段中的值数量一致。"

#: ../../source/finetune/llamafactory.md:82 977f7b7616974274a78b9de605d153aa
msgid "Building Video Dataset"
msgstr "构建视频数据集"

#: ../../source/finetune/llamafactory.md:84 8645ead0e65e4f7eb3ca408fd5613c40
msgid ""
"Refer to the **mllm_video_demo.json** dataset under [LLaMA-"
"Factory/data](https://github.com/hiyouga/LLaMA-"
"Factory/blob/main/data/dataset_info.json) and construct your data in the "
"same format. The structure is as follows:"
msgstr ""
"参考 [LLaMA-Factory/data](https://github.com/hiyouga/LLaMA-"
"Factory/blob/main/data/dataset_info.json) 下的 **mllm_video_demo.json** "
"数据集，按照相同格式构建你的数据。结构如下："

#: ../../source/finetune/llamafactory.md:86 db2a76230fd84f3aab964f7a576e5447
msgid ""
"To use videos in multi-turn conversations, add the `<video>` tag in the "
"user's content for each turn, and add the corresponding video paths in "
"the `videos` field. The number of `<video>` tags should match the number "
"of values in `videos`."
msgstr ""
"如需在多轮对话中使用图片，请在每轮用户内容中添加 `<video>` 标签，并在 `videos` 字段中添加对应的图片路径。`<video>` "
"标签的数量应与 `videos` 字段中的值数量一致。"

#: ../../source/finetune/llamafactory.md:108 10cb96d2f14d49c4abd18b0d4f67963f
msgid "Building Audio Dataset"
msgstr "构建音频数据集"

#: ../../source/finetune/llamafactory.md:110 c602a61519504fc48833cf86c0a14303
msgid "**Note: Only MiniCPM-o 2.6 model supports audio fine-tuning**"
msgstr "**注意：仅MiniCPM-o 2.6模型支持音频微调**"

#: ../../source/finetune/llamafactory.md:112 8645ead0e65e4f7eb3ca408fd5613c40
msgid ""
"Refer to the **mllm_audio_demo.json** dataset under [LLaMA-"
"Factory/data](https://github.com/hiyouga/LLaMA-"
"Factory/blob/main/data/dataset_info.json) and construct your data in the "
"same format. The structure is as follows:"
msgstr ""
"参考 [LLaMA-Factory/data](https://github.com/hiyouga/LLaMA-"
"Factory/blob/main/data/dataset_info.json) 下的 **mllm_audio_demo.json** "
"数据集，按照相同格式构建你的数据。结构如下："

#: ../../source/finetune/llamafactory.md:114 db2a76230fd84f3aab964f7a576e5447
msgid ""
"To use audio in multi-turn conversations, add the `<audio>` tag in the "
"user's content for each turn, and add the corresponding audio paths in "
"the `audios` field. The number of `<audio>` tags should match the number "
"of values in `audios`."
msgstr ""
"如需在多轮对话中使用图片，请在每轮用户内容中添加 `<audio>` 标签，并在 `audios` 字段中添加对应的图片路径。`<audio>` "
"标签的数量应与 `audios` 字段中的值数量一致。"

#: ../../source/finetune/llamafactory.md:136 9349c4388dfd43e0b8324f569d86cc7b
msgid "Register Dataset"
msgstr "注册数据集"

#: ../../source/finetune/llamafactory.md:138 23314332c2a340fa9a8f71f0747e65e1
msgid ""
"Name your constructed JSON file as `image_caption.json` and place it "
"under `LLaMA-Factory/data/`."
msgstr "将你构建的 JSON 文件命名为 `image_caption.json`，并放置在 `LLaMA-Factory/data/` 目录下。"

#: ../../source/finetune/llamafactory.md:140 514b33b07d224b2cb90feff49d0f681d
msgid "Locate `LLaMA-Factory/data/dataset_info.json`."
msgstr "定位到 `LLaMA-Factory/data/dataset_info.json` 文件。"

#: ../../source/finetune/llamafactory.md:142 bf68ad02328846e6917a008b52f41bea
msgid "Search for `mllm_demo` and find the following field:"
msgstr "查找 `mllm_demo` 字段，并找到如下内容："

#: ../../source/finetune/llamafactory.md:154 cbb2df9f75394dd89073876d6562c6b2
msgid ""
"Change the **key** `mllm_demo` to your custom dataset name, e.g., "
"`cpmv_img`."
msgstr "将 **键值** `mllm_demo` 修改为你的自定义数据集名称，例如 `cpmv_img`。"

#: ../../source/finetune/llamafactory.md:156 5e64222259b5471fb23c105617c1ab49
msgid ""
"Change the `file_name` value to your constructed dataset name, e.g., "
"`image_caption.json`."
msgstr "将 `file_name` 的值修改为你构建的数据集名称，例如 `image_caption.json`。"

#: ../../source/finetune/llamafactory.md:158 48ea59420187434ea70a6d366907cc41
msgid "Example:"
msgstr "示例："

#: ../../source/finetune/llamafactory.md:177 7fefc8cde83040d694c325424822b16c
msgid ""
"For datasets containing videos and audio, please refer to the following "
"format:"
msgstr "对于包含视频和音频的数据集，请参照下列格式"

#: ../../source/finetune/llamafactory.md:197 bd4da40f7ccd4addbb3c53436233d45a
msgid "Create Training Configuration YAML Files"
msgstr "创建训练配置 YAML 文件"

#: ../../source/finetune/llamafactory.md:199 ec2ff251068942e89dfad6de57107cd1
msgid "LoRA Fine-tuning"
msgstr "LoRA 微调"

#: ../../source/finetune/llamafactory.md:201 20e744b25dda4f4da92687816007ec09
msgid ""
"Create a configuration file named `minicpmv4_5_lora_sft.yaml` and place "
"it in `LLaMA-Factory/minicpm_config`."
msgstr ""
"创建名为 `minicpmv4_5_lora_sft.yaml` 的配置文件，并放置在 `LLaMA-"
"Factory/minicpm_config` 目录下。"

#: ../../source/finetune/llamafactory.md:245 43b5ce58421f415583daefbe99d8cf74
msgid "Full Fine-tuning"
msgstr "全量微调"

#: ../../source/finetune/llamafactory.md:247 422fcd2d4e694704b290ca3b552a7e0d
msgid ""
"Create a full training configuration file `minicpmv4_5_full_sft.yaml` and"
" place it in `LLaMA-Factory/minicpm_config`:"
msgstr ""
"创建全量训练配置文件 `minicpmv4_5_full_sft.yaml`，并放置在 `LLaMA-"
"Factory/minicpm_config` 目录下："

#: ../../source/finetune/llamafactory.md:294 1a6714a3a9f84f42afa9a84be3111c0b
msgid "Model Training"
msgstr "模型训练"

#: ../../source/finetune/llamafactory.md:296 16ad943e70904bb8ae266263542ee0f2
msgid "Full Training"
msgstr "全量训练"

#: ../../source/finetune/llamafactory.md:303 ce3b1346735b4c1587a96ee95f05c4f9
msgid "LoRA Training"
msgstr "LoRA 训练"

#: ../../source/finetune/llamafactory.md:305 4093b9e3a97b45fca788c72d22651636
msgid "Start training:"
msgstr "开始训练："

#: ../../source/finetune/llamafactory.md:311 481a4278dedb48daa25c6f7ee92d30a2
msgid "Create a merge script `merge.yaml`:"
msgstr "创建合并脚本 `merge.yaml`："

#: ../../source/finetune/llamafactory.md:328 ab4cf98e3e0f4df58fa81472d6fc64d6
msgid "Merge the model:"
msgstr "合并模型："

#~ msgid "**Support:** MiniCPM-V 4.0 / MiniCPM-V 2.6 / MiniCPM-V 2.5"
#~ msgstr "**支持:** MiniCPM-V 4.0 / MiniCPM-V 2.6 / MiniCPM-V 2.5"

#~ msgid ""
#~ "Copy this field, modify the highlighted"
#~ " parts as per your dataset, and "
#~ "add it to `LLaMA-"
#~ "Factory/data/dataset_info.json`."
#~ msgstr "复制该字段，根据你的数据集修改高亮部分，并添加到 `LLaMA-Factory/data/dataset_info.json` 文件中。"

