# Demo Applications Guide

Complete web demo applications to quickly experience and showcase MiniCPM-o's powerful capabilities.

📖 [中文版本](./README_zh.md) | [Back to Main](../)

## Web Demo

We provide two types of web demo applications for different use cases:

### 📖 Text & Image Demo

**Best for**: Standard multimodal interactions, document processing, image analysis

**Features**:

- Text and image-based interactions
- Multi-turn conversations with uploaded files
- Perfect for research, education, and basic multimodal tasks
- Supports various image formats and document parsing

**Available Versions**:

- **Gradio**: Clean and easy-to-use web interface with quick deployment
- **OpenWebUI**: Advanced web interface with enhanced features

### 🎤 Omni Streaming Demo

**Best for**: Real-time voice conversations, multimodal streaming, voice interaction

**Features**:

- Real-time voice conversation capabilities
- Audio and video streaming support
- Voice cloning and emotion control
- Simultaneous processing of multiple modalities
- API endpoints for streaming interactions

## Use Cases

### Omni Streaming Demo

- **Voice Assistants**: Conversational AI with voice interaction
- **Streaming Applications**: Multimodal streaming interfaces
- **Customer Service**: Voice-enabled support systems
- **Entertainment**: Voice cloning and interactive media
