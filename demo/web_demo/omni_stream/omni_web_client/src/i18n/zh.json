{"menuTabVideo": "实时视频通话", "menuTabAudio": "实时语音通话", "menuTabChatbot": "聊天机器人", "videoCallBtn": "视频通话", "audioCallBtn": "语音通话", "hangUpBtn": "挂断", "notReadyBtn": "服务繁忙，请稍后", "skipMessageBtn": "跳过当前对话", "feedbackDialogTitle": "请输入反馈意见", "modelConfigTitle": "模型配置", "audioInterruptionBtn": "语音打断", "audioInterruptionTips": "开启\"语音打断\"功能，支持在模型说话时打断模型，模型会立刻结束上一轮的生成，并支持用户最新的问题。", "yes": "是", "no": "否", "videoQualityBtn": "高清模式", "videoQualityTips": "开启高清模式，模型会在最后一帧对图片进行高清编码，可以使得模型看得清更细节的部分。", "high": "高清", "low": "低清", "vadThresholdBtn": "VAD阈值", "vadThresholdTips": "vad阈值表示声音静音多久才开始触发推理，vad阈值过低会导致说话气口误触，过高会导致首响更慢。", "assistantPromptBtn": "任务指令", "assistantPromptTips": "模型的任务指令，用于支持不同的任务目标", "useVoicePromptBtn": "音色指令", "voiceClonePromptInput": "音色指令", "voiceClonePromptTips": "我们的模型具有端到端的音色克隆能力，提供一段 5-7 秒的音频，模型在一定程度上可以用这种音色来说话。但基于法律考虑，我们的demo并不开启这个能力的试用。社区可以参照我们的开源代码自行适配。", "audioChoiceBtn": "音色选择", "defaultAudioBtn": "默认音色", "customizationBtn": "自定义：上传音频", "toneColorOptions": "语音选项", "toneColorOptionsTips": "我们提供了一些示例音色，也可以选择“无”并通过指令让模型创建音色。", "nullOption": "无", "defaultOption": "女一号(默认)", "femaleOption": "女二号", "maleOption": "男一号"}