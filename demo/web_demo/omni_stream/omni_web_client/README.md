## Language

-   [English](#english)
-   [中文](#中文)

---

# English

## important

This project depends on Node and PNPM. If they are not installed, please install them.


## Project Setup

```sh
pnpm install
```

## Compile and Hot-Reload for Development

```sh
pnpm run dev
```

## Compile and Minify for Production

```sh
pnpm run build
```

### Tips

If you want to use your own backend in the development environment, please modify the proxy object in <font color="red">vite.config.js</font> located in the root directory.

### Recommended IDE Setup

[VSCode](https://code.visualstudio.com/)

---

# 中文

## 重要

这个项目依赖于node、pnpm环境，如果你的PC上没有，请先安装。

## 安装依赖

```sh
pnpm install
```

## 运行在本地开发模式下（可热更新）

```sh
pnpm run dev
```

## 编译代码（用于生产环境）

```sh
pnpm run build
```

### 注意

如果你想在本地开发模式下运行项目，并且调用自己的后端服务，请修改项目根目录下的<font color="red">vite.config.js</font>文件中的proxy配置。

### 推荐IDE

[VSCode](https://code.visualstudio.com/)

