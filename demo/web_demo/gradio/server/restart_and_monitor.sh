#!/bin/bash

# 重启gradio server并监控日志的脚本

echo "======================================"
echo "重启MiniCPM-V代理服务器"
echo "======================================"

# 杀死现有的gradio server进程
echo "停止现有的gradio server进程..."
pkill -f "gradio_server.py" || true
sleep 2

# 清空日志文件
echo "清空日志文件..."
rm -f logs/log_rank0.log
mkdir -p logs

# 启动新的server
echo "启动新的gradio server..."
nohup python gradio_server.py \
    --port 9999 \
    --api_base_url "http://172.16.3.112:8080/v1" \
    --api_key "" \
    --log_dir "logs" \
    --instance_id 0 > /dev/null 2>&1 &

# 等待服务器启动
sleep 3

echo "======================================"
echo "服务器启动完成，开始监控日志..."
echo "按 Ctrl+C 停止监控"
echo "======================================"

# 实时监控日志
tail -f logs/log_rank0.log
