from pydantic import BaseModel
import uvicorn
import fastapi
from fastapi.responses import StreamingResponse
import argparse
import logging
import json
from openai_client import OpenAIClient
from io import BytesIO
from PIL import Image
import base64

from logging_util import setup_root_logger

logger = logging.getLogger(__name__)


class Model:
    def __init__(self, api_base_url: str, api_key: str = "", instance_id: int = 0) -> None:
        self.instance_id = instance_id
        self.api_base_url = api_base_url
        self.api_key = api_key
        
        logger.info(f"实例 {instance_id}: 初始化OpenAI客户端")
        logger.info(f"实例 {instance_id}: API地址: {api_base_url}")
        
        self.client = OpenAIClient(base_url=api_base_url, api_key=api_key)
        
        logger.info(f"实例 {instance_id}: OpenAI客户端初始化完成")

    def handler(self, query):
        # 解析输入参数
        image = self._decode_image(query.get("image"))
        question = query.get("question", "")
        params = json.loads(query.get("params", "{}"))
        
        # 解析消息格式
        if isinstance(question, str):
            try:
                msgs = json.loads(question)
            except json.JSONDecodeError:
                msgs = [{"role": "user", "content": question}]
        else:
            msgs = question
        
        # 检查消息中是否包含图片数据（gradio client格式）
        if not image and msgs:
            logger.info(f"尝试从消息中提取图片数据...")
            image = self._extract_image_from_messages(msgs)
            logger.info(f"从消息提取图片结果: {image is not None}")
        
        # 调用OpenAI API
        try:
            response = self.client.chat_completion(
                messages=msgs,
                image=image,
                **params
            )
            
            # 提取响应内容
            choices = response.get('choices', [])
            if choices:
                content = choices[0].get('message', {}).get('content', '')
                usage = response.get('usage', {})
                output_tokens = usage.get('completion_tokens', 0)
            else:
                content = ""
                output_tokens = 0
            
            return {
                "result": content,
                "usage": {"output_tokens": output_tokens}
            }
            
        except Exception as e:
            logger.error(f"API调用失败: {e}")
            return {
                "result": f"错误: {str(e)}",
                "usage": {"output_tokens": 0}
            }

    def stream_handler(self, query):
        # 解析输入参数
        image = self._decode_image(query.get("image"))
        question = query.get("question", "")
        params = json.loads(query.get("params", "{}"))
        params["stream"] = True
        
        # 解析消息格式
        if isinstance(question, str):
            try:
                msgs = json.loads(question)
            except json.JSONDecodeError:
                msgs = [{"role": "user", "content": question}]
        else:
            msgs = question
        
        # 检查消息中是否包含图片数据（gradio client格式）
        if not image and msgs:
            logger.info(f"流式：尝试从消息中提取图片数据...")
            image = self._extract_image_from_messages(msgs)
            logger.info(f"流式：从消息提取图片结果: {image is not None}")
        
        # 调用OpenAI流式API
        try:
            generator = self.client.chat_completion_stream(
                messages=msgs,
                image=image,
                **params
            )
            return generator
            
        except Exception as e:
            logger.error(f"流式API调用失败: {e}")
            def error_generator():
                yield f"错误: {str(e)}"
            return error_generator()
    
    def _decode_image(self, image_data):
        """解码base64图片数据"""
        if not image_data or len(image_data) <= 10:
            return None
        
        try:
            image = Image.open(BytesIO(base64.b64decode(image_data))).convert('RGB')
            return image
        except Exception as e:
            logger.error(f"图片解码失败: {e}")
            return None
    
    def _extract_image_from_messages(self, msgs):
        """从消息中提取图片数据（gradio client格式）"""
        if not msgs or not isinstance(msgs, list):
            return None
            
        for msg in msgs:
            if isinstance(msg, dict):
                # 检查 content 或 contents 字段
                content = msg.get('content', msg.get('contents', []))
                if isinstance(content, list):
                    for item in content:
                        if isinstance(item, dict) and item.get('type') == 'image':
                            image_data = item.get('pairs', '')
                            if image_data:
                                logger.info(f"从消息中提取到图片数据，长度: {len(image_data)}")
                                return self._decode_image(image_data)
        return None


class Item(BaseModel):
    image: str
    question: str
    params: str
    temporal_ids: str = None

model = None
args = None

def initialize_server():
    """初始化服务器配置和模型"""
    global model, args
    
    parser = argparse.ArgumentParser(description='Server for MiniCPM-V with OpenAI API')
    parser.add_argument('--port', type=int, default=9999,
                        help='Port to run the server on')
    parser.add_argument('--log_dir', type=str, default='logs',
                        help='Directory for log files')
    parser.add_argument('--api_base_url', type=str, default='http://************:8080/v1',
                        help='Base URL for OpenAI compatible API')
    parser.add_argument('--api_key', type=str, default='',
                        help='API key for OpenAI compatible API')
    parser.add_argument('--instance_id', type=int, default=0,
                        help='Instance ID for multi-instance deployment')
    args = parser.parse_args()

    setup_root_logger(local_dir=args.log_dir)

    # 打印实例信息
    logger.info(f"="*50)
    logger.info(f"启动MiniCPM-V代理服务实例")
    logger.info(f"实例ID: {args.instance_id}")
    logger.info(f"端口: {args.port}")
    logger.info(f"API地址: {args.api_base_url}")
    logger.info(f"API密钥: {args.api_key}")
    logger.info(f"日志目录: {args.log_dir}")
    logger.info(f"="*50)

    model = Model(args.api_base_url, args.api_key, args.instance_id)

app = fastapi.FastAPI()


@app.get("/")
def read_root():
    return {
        "message": "MiniCPM-V proxy server", 
        "instance_id": args.instance_id,
        "port": args.port,
        "api_base_url": args.api_base_url,
        "status": "running"
    }


@app.post("/api")
def websocket(item: Item):
    logger.info(f'收到API请求')
    logger.info(f'image长度: {len(item.image) if item.image else 0}')
    logger.info(f'question: {item.question[:100]}...' if len(str(item.question)) > 100 else item.question)
    logger.info(f'params: {str(item.params)}')
    
    query = item.dict()
    res = model.handler(query)

    logger.info(f'API响应: {str(res)[:200]}...' if len(str(res)) > 200 else str(res))
    return {'data': res}


def format_question_for_log(question):
    """格式化question用于日志输出，省略pairs字段"""
    import copy
    import json

    if not question:
        return question

    try:
        # 如果question是字符串，先解析为Python对象
        if isinstance(question, str):
            try:
                parsed_question = json.loads(question)
            except json.JSONDecodeError:
                # 如果不是JSON格式，直接返回原字符串
                return question
        else:
            parsed_question = question

        # 深拷贝避免修改原始数据
        formatted_question = copy.deepcopy(parsed_question)

        # 如果question是列表，处理每个元素
        if isinstance(formatted_question, list):
            for item in formatted_question:
                if isinstance(item, dict) and 'contents' in item:
                    if isinstance(item['contents'], list):
                        for content in item['contents']:
                            if isinstance(content, dict) and content.get('type') == 'image' and 'pairs' in content:
                                pairs_len = len(content['pairs']) if content['pairs'] else 0
                                content['pairs'] = f"<省略{pairs_len}字符的数据>"

        return formatted_question
    except Exception as e:
        # 如果处理失败，返回简化的信息
        return f"<question格式化失败: {str(e)}>"

@app.post("/api/stream")
def stream_api(item: Item):
    logger.info(f'收到流式API请求')
    logger.info(f'image长度: {len(item.image) if item.image else 0}')
    # logger.info(f'原始question类型: {type(item.question)}')
    formatted_question = format_question_for_log(item.question)
    # logger.info(f'格式化后question类型: {type(formatted_question)}')
    logger.info(f'question: {str(formatted_question)}')
    logger.info(f'params: {str(item.params)}')
    
    query = item.dict()
    def event_generator():
        try:
            generator = model.stream_handler(query)
            full_response = ""
            output_tokens = 0
            
            for chunk in generator:
                full_response += chunk
                output_tokens += 1
                data = {
                    "chunk": chunk,
                    "full_response": full_response,
                    "finished": False
                }
                yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
            
            final_data = {
                "chunk": "",
                "full_response": full_response,
                "finished": True
            }
            yield f"data: {json.dumps(final_data, ensure_ascii=False)}\n\n"
            
        except Exception as e:
            error_data = {
                "error": str(e),
                "finished": True
            }
            logger.error(f"Stream error: {e}")
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        event_generator(), 
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

if __name__ == "__main__":
    initialize_server()
    
    _cfg = uvicorn.Config(app, host="0.0.0.0", port=args.port, workers=2)
    uvicorn.Server(_cfg).run()
