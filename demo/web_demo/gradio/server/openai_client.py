import requests
import json
import base64
import logging
from typing import Dict, Any, Generator, Optional
from io import BytesIO
from PIL import Image

logger = logging.getLogger(__name__)


class OpenAIClient:
    """OpenAI兼容的HTTP客户端"""
    
    def __init__(self, base_url: str = "http://************:8080/v1", api_key: str = "dummy"):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
    def _prepare_messages(self, msgs: list, image: Optional[Image.Image] = None) -> list:
        """准备符合OpenAI格式的消息"""
        formatted_messages = []
        
        for msg in msgs:
            if isinstance(msg, dict):
                role = msg.get('role', 'user')
                # 兼容 content 和 contents 两种格式
                content = msg.get('content', msg.get('contents', []))
                
                # 处理内容
                formatted_content = []
                
                if isinstance(content, str):
                    formatted_content.append({"type": "text", "text": content})
                elif isinstance(content, list):
                    for item in content:
                        if isinstance(item, str):
                            formatted_content.append({"type": "text", "text": item})
                        elif isinstance(item, dict):
                            if item.get('type') == 'text':
                                formatted_content.append({"type": "text", "text": item.get('pairs', '')})
                            elif item.get('type') == 'image':
                                # 处理base64图片
                                image_data = item.get('pairs', '')
                                formatted_content.append({
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{image_data}"
                                    }
                                })
                        elif isinstance(item, Image.Image):
                            # 将PIL图片转为base64
                            buffer = BytesIO()
                            item.save(buffer, format='JPEG')
                            img_base64 = base64.b64encode(buffer.getvalue()).decode()
                            formatted_content.append({
                                "type": "image_url", 
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{img_base64}"
                                }
                            })
                
                # 如果有单独的image参数，添加到第一个用户消息中
                if image and role == 'user' and not any(item.get('type') == 'image_url' for item in formatted_content):
                    buffer = BytesIO()
                    image.save(buffer, format='JPEG')
                    img_base64 = base64.b64encode(buffer.getvalue()).decode()
                    formatted_content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{img_base64}"
                        }
                    })
                
                formatted_messages.append({
                    "role": role,
                    "content": formatted_content if formatted_content else content
                })
        
        return formatted_messages
    
    def chat_completion(self, messages: list, image: Optional[Image.Image] = None, **params) -> Dict[str, Any]:
        """调用OpenAI兼容的chat completion API"""
        formatted_messages = self._prepare_messages(messages, image)
        
        payload = {
            "model": "MiniCPM-V-4_5-F16.gguf",  # 使用服务器支持的模型名
            "messages": formatted_messages,
            **params
        }
        
        # 移除不支持的参数
        payload.pop('enable_thinking', None)
        payload.pop('temporal_ids', None)
        
        logger.info(f"发送请求到 {self.base_url}/chat/completions")
        logger.info(f"消息数量: {len(formatted_messages)}")
        logger.debug(f"请求payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=300
            )
            response.raise_for_status()
            
            result = response.json()
            logger.debug(f"API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP请求错误: {e}")
            raise Exception(f"API调用失败: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            raise Exception(f"响应解析失败: {e}")
    
    def chat_completion_stream(self, messages: list, image: Optional[Image.Image] = None, **params) -> Generator[str, None, None]:
        """调用OpenAI兼容的流式chat completion API"""
        formatted_messages = self._prepare_messages(messages, image)
        
        payload = {
            "model": "MiniCPM-V-4_5-F16.gguf",
            "messages": formatted_messages,
            "stream": True,
            **params
        }
        
        # 移除不支持的参数
        payload.pop('enable_thinking', None)
        payload.pop('temporal_ids', None)
        
        logger.info(f"发送流式请求到 {self.base_url}/chat/completions")
        logger.info(f"消息数量: {len(formatted_messages)}")
        logger.debug(f"请求payload: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                stream=True,
                timeout=300
            )
            response.raise_for_status()
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        
                        if data_str.strip() == '[DONE]':
                            break
                            
                        try:
                            data = json.loads(data_str)
                            choices = data.get('choices', [])
                            if choices:
                                delta = choices[0].get('delta', {})
                                content = delta.get('content', '')
                                if content:
                                    yield content
                        except json.JSONDecodeError:
                            # 跳过无法解析的行
                            continue
                            
        except requests.exceptions.RequestException as e:
            logger.error(f"流式HTTP请求错误: {e}")
            raise Exception(f"流式API调用失败: {e}")
