# 演示应用指南

提供完整的 Web 演示应用，帮助您快速体验和展示 MiniCPM-o 的强大功能。

📖 [English Version](./README.md) | [返回主页](../)

## Web Demo

我们提供两种类型的 Web 演示应用，适用于不同的使用场景：

### 📖 图文演示

**最适合**: 标准多模态交互、文档处理、图像分析

**特点**:

- 基于文本和图像的交互
- 支持文件上传的多轮对话
- 适合研究、教育和基础多模态任务
- 支持多种图像格式和文档解析

**可用版本**:

- **Gradio**: 简洁易用的 Web 界面，支持快速部署
- **OpenWebUI**: 高级 Web 界面，具有增强功能

### 🎤 全模态流式演示

**最适合**: 实时语音对话、多模态流式处理、语音交互

**特点**:

- 实时语音对话能力
- 音视频流处理支持
- 语音克隆和情感控制
- 多模态同时处理
- 流式交互 API 端点

## 使用场景

### 全模态流式演示

- **语音助手**: 支持语音交互的对话 AI
- **流媒体应用**: 多模态流式交互界面
- **客户服务**: 语音客服系统
- **娱乐应用**: 语音克隆和互动媒体
